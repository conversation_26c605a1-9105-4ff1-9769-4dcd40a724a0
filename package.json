{"name": "truyenhay", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "npm run build && wrangler pages dev", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "deploy": "npm run build && npm run db:migrate:prod && wrangler pages deploy", "cf-typegen": "wrangler types src/worker-configuration.d.ts", "db:migrate:local": "drizzle-kit migrate", "db:migrate:prod": "wrangler d1 migrations apply DB --remote"}, "devDependencies": {"@eslint/compat": "^1.3.0", "@eslint/js": "^9.29.0", "@sveltejs/adapter-cloudflare": "^7.0.4", "@sveltejs/kit": "^2.22.0", "@sveltejs/vite-plugin-svelte": "^5.1.0", "@tailwindcss/vite": "^4.1.10", "@types/better-sqlite3": "^7.6.13", "drizzle-kit": "^0.31.1", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-svelte": "^3.9.3", "globals": "^16.2.0", "prettier": "^3.5.3", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.13", "svelte": "^5.34.7", "svelte-check": "^4.2.2", "tailwindcss": "^4.1.10", "typescript": "^5.8.3", "typescript-eslint": "^8.34.1", "vite": "^6.3.5", "wrangler": "^4.20.5"}, "dependencies": {"better-sqlite3": "^11.10.0", "drizzle-orm": "^0.44.2", "openai": "^5.6.0"}}