// src/lib/server/ai.ts

import OpenAI from 'openai';

interface StoryContent {
	title: string;
	content: string;
}

/**
 * Generates a short story using the OpenAI API.
 * Instructs the model to return a structured JSON object.
 * @param apiKey The OpenAI API key.
 * @returns A promise that resolves to an object containing the story's title and content.
 * @throws Will throw an error if the API key is missing or the API call fails.
 */
export async function generateStoryContent(apiKey: string): Promise<StoryContent> {
	if (!apiKey) {
		throw new Error('OpenAI API key is not configured.');
	}

	const openai = new OpenAI({ apiKey });

	const chatCompletion = await openai.chat.completions.create({
		messages: [
			{
				role: 'system',
				content: `
					# ROLE & GOAL
					You are an expert web novel author, a master of the "Trọng Sinh Đô Thị Tu Tiên" and "Dị Năng Nghịch Thiên" genres. You are fluent in Vietnamese web novel slang and writing style. Your goal is to craft a compelling, fast-paced short story that hooks the reader from the very first paragraph and leaves them wanting more.

					# CONTEXT
					The story's theme is: "<PERSON>r<PERSON><PERSON> sinh báo thù, sở hữu sức mạnh nghịch thiên." (Rebirth for revenge, possessing a heaven-defying power). The setting is modern-day Vietnam, but with hidden supernatural elements (cultivation, special abilities, ancient artifacts, etc.). The main character (MC) is someone who was a failure or met a tragic end in their previous life.

					# TASK
					Your primary task is to generate a complete short story (approximately 800-1200 words) based on the specific user prompt. The final output **MUST** be a single, valid JSON object containing exactly two keys:
					1.  'title': A catchy, appealing web novel title in Vietnamese.
					2.  'content': The full story text in Vietnamese, formatted using simple Markdown (e.g., using '###' for subheadings, '*text*' for italics, and '**text**' for bold).

					# MANDATORY STORY STRUCTURE & KEY ELEMENTS
					The generated story must follow this narrative arc precisely:

					1.  **Part 1: The Fall and Rebirth (Cú Ngã và Trọng Sinh):**
						* Start by briefly describing the MC's miserable death or moment of ultimate failure in their previous life (e.g., betrayed, destitute, powerless).
						* The MC suddenly wakes up. They are reborn into their younger self at a specific, critical, and often humiliating moment in their past. Clearly describe their shock, disbelief, then the burning rage and determination as they realize they have a second chance.

					2.  **Part 2: The Golden Finger (Kim Thủ Chỉ):**
						* Immediately after rebirth, the MC discovers their "heaven-defying power" or "golden finger". This could be a System ('Hệ thống'), a special ability, an ancient artifact that followed them back, or memories of future technology/knowledge.
						* Describe the discovery moment clearly. If it's a system, describe its interface (e.g., a mental screen, a robotic voice). Show its first, incredibly overpowered function.

					3.  **Part 3: The First "Face Slap" (Màn Vả Mặt Đầu Tiên):**
						* This is the most critical part of the opening. The MC must immediately use their newfound power or knowledge to resolve the humiliating situation they woke up in.
						* They must shock and awe the people who were looking down on them (e.g., an arrogant rival, a scornful ex-girlfriend, condescending relatives). This "face-slap" moment must be satisfying and decisive.

					4.  **Part 4: The Climax & Future Hook (Cao Trào & Gợi Mở Tương Lai):**
						* Provide a small-scale climax that solidifies the MC's new, powerful status and hints at their potential.
						* End the story with a "hook". The MC should have a clear goal for the future, usually involving taking revenge on the main antagonist from their previous life or resolving a deep regret. This leaves the reader wanting to know what happens next.

					# STYLE & TONE
					* **Language:** Vietnamese.
					* **Pacing:** Fast, direct, and action-oriented. Avoid lengthy descriptions of scenery. Focus on the MC's actions and thoughts.
					* **Inner Monologue:** Use inner thoughts (nội tâm) extensively to show the MC's emotions (shock, rage, cold calculation, excitement).
					* **Key Vocabulary:** Seamlessly integrate common web novel terms where appropriate, such as: "phế vật" (trash/failure), "tu vi" (cultivation level), "hệ thống" (system), "nghịch thiên" (heaven-defying), "vả mặt" (face-slapping), "kiếp trước" (previous life), "sống lại" (reborn).
					`
			},
			{
				role: 'user',
				content:
					'A programmer who died from overwork is reborn on the day he was publicly humiliated and dumped by his girlfriend. He awakens a "Divine Coder System".'
			}
		],
		model: 'gpt-4o',
		response_format: { type: 'json_object' }
	});

	const responseContent = chatCompletion.choices[0].message.content;

	if (!responseContent) {
		throw new Error('Failed to generate story content from OpenAI.');
	}

	return JSON.parse(responseContent) as StoryContent;
}
