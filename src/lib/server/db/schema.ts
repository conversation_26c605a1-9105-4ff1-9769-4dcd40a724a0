import { sqliteTable, text, integer } from 'drizzle-orm/sqlite-core';

export const stories = sqliteTable('stories', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	title: text('title').notNull(),
	content: text('content').notNull(),
	slug: text('slug').notNull().unique(),
	createdAt: text('created_at').notNull()
});

export type Story = typeof stories.$inferSelect;
export type NewStory = typeof stories.$inferInsert;
