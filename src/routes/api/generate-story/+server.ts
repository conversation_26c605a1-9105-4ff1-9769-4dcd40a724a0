// src/routes/api/generate-story/+server.ts

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { createDbClient } from '$lib/server/db';
import { stories } from '$lib/server/db/schema';
import { generateStoryContent } from '$lib/server/ai';
import { slugify } from '$lib/utils/slugify';

export const GET: RequestHandler = async ({ platform }) => {
	const dbBinding = platform?.env.DB;
	const apiKey = platform?.env.OPENAI_API_KEY;

	if (!dbBinding || !apiKey) {
		console.error('Server configuration error: Missing DB or OPENAI_API_KEY binding.');
		return json({ error: 'Server is not configured correctly.' }, { status: 500 });
	}

	try {
		// 1. Generate story content from AI
		const { title, content } = await generateStoryContent(apiKey);

		// 2. Prepare data for database insertion
		const newStory = {
			title,
			content,
			slug: `${slugify(title)}-${Date.now()}`, // Append timestamp for uniqueness
			createdAt: new Date().toISOString()
		};

		// 3. Initialize Drizzle client and save to D1
		const db = createDbClient(dbBinding);
		const insertedStory = await db.insert(stories).values(newStory).returning();

		// 4. Return success response
		return json({ success: true, story: insertedStory[0] }, { status: 201 });
		
	} catch (error) {
		console.error('Failed to generate and save story:', error);
		const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred.';
		return json({ error: 'Failed to process the request.', details: errorMessage }, { status: 500 });
	}
};
